# OG Image Instructions

## ⚠️ CRITICAL: Missing OG Image

File `og-image.jpg` direferensikan di metadata tapi tidak ada. Ini akan menyebabkan broken image di social media shares.

## 📐 Spesifikasi OG Image

### Ukuran & Format
- **Dimensi:** 1200 x 630 pixels (rasio 1.91:1)
- **Format:** JPG atau PNG
- **Ukuran file:** < 1MB (idealnya < 500KB)
- **Nama file:** `og-image.jpg`

### Konten yang Harus Ada
1. **Logo/Brand name:** "Landing Template" atau logo brand
2. **Tagline:** "Modern & Fast Landing Pages"
3. **Visual elements:** Gradient, geometric shapes, atau screenshot
4. **Readable text:** Pastikan teks terbaca di thumbnail kecil

### Design Guidelines
- **Background:** Gradient atau solid color yang menarik
- **Typography:** Bold, readable fonts (min 24px)
- **Contrast:** High contrast untuk readability
- **Branding:** Konsisten dengan theme aplikasi
- **Safe area:** Hindari teks penting di tepi (margin 60px)

## 🎨 Tools untuk Membuat OG Image

### Online Tools (Mudah)
1. **Canva** - Template OG image tersedia
2. **Figma** - Design custom dengan template
3. **Pablo by Buffer** - Quick OG image generator
4. **Bannerbear** - Automated OG image generation

### Design Software
1. **Adobe Photoshop** - Professional design
2. **Sketch** - Mac-based design tool
3. **GIMP** - Free alternative

### Code-based (Advanced)
1. **Vercel OG** - Generate with code
2. **Puppeteer** - Screenshot HTML/CSS
3. **Canvas API** - Generate with JavaScript

## 🚀 Quick Implementation

### Option 1: Vercel OG (Recommended)
```typescript
// app/api/og/route.tsx
import { ImageResponse } from 'next/og';

export async function GET() {
  return new ImageResponse(
    (
      <div
        style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'column',
          fontFamily: 'Inter',
        }}
      >
        <h1 style={{ fontSize: 60, color: 'white', margin: 0 }}>
          Landing Template
        </h1>
        <p style={{ fontSize: 30, color: 'white', margin: 0 }}>
          Modern & Fast Landing Pages
        </p>
      </div>
    ),
    {
      width: 1200,
      height: 630,
    }
  );
}
```

### Option 2: Static Image
1. Buat image dengan tools di atas
2. Save sebagai `public/og-image.jpg`
3. Pastikan ukuran 1200x630px

## ✅ Checklist

- [ ] Buat og-image.jpg (1200x630px)
- [ ] Place di folder `public/`
- [ ] Test di social media debugger:
  - [Facebook Debugger](https://developers.facebook.com/tools/debug/)
  - [Twitter Card Validator](https://cards-dev.twitter.com/validator)
  - [LinkedIn Post Inspector](https://www.linkedin.com/post-inspector/)
- [ ] Verify image loads di browser: `/og-image.jpg`

## 🔗 Referensi
- [Open Graph Protocol](https://ogp.me/)
- [Twitter Cards Guide](https://developer.twitter.com/en/docs/twitter-for-websites/cards/overview/abouts-cards)
- [Vercel OG Image Generation](https://vercel.com/docs/concepts/functions/edge-functions/og-image-generation)

**Priority: CRITICAL** - Implementasi segera untuk menghindari broken social shares.
